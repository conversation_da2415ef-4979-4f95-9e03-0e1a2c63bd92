#!/usr/bin/env node

/**
 * Manual test script for exception publishing functionality
 * This script tests the Cronus service exception publishing endpoints
 */

const axios = require('axios');
const { ExceptionPublisherAction } = require('./packages/cronus-client/dist/index.js');

const BASE_URL = 'http://localhost:9876';

async function testExceptionPublishing() {
  console.log('🚀 Starting Exception Publishing Tests...\n');

  try {
    // Test 1: Health Check
    console.log('📋 Test 1: Health Check...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      console.log('   ✅ Health check passed:', healthResponse.data);
    } catch (error) {
      console.log('   ❌ Health check failed:', error.message);
      console.log('   💡 Make sure the service is running on port 3001');
      return;
    }

    // Test 2: Publish Business Exception
    console.log('\n📋 Test 2: Publishing Business Exception...');
    const exceptionPayload = {
      tenantId: 'test-tenant-123',
      definitionCode: 'PAYMENT_FAILED',
      exceptionInfo: {
        amount: 250.50,
        currency: 'USD',
        orderId: 'order-789',
        paymentMethod: 'credit_card',
        errorCode: 'INSUFFICIENT_FUNDS',
        timestamp: new Date().toISOString()
      },
      message: 'Payment failed due to insufficient funds'
    };

    try {
      const publishResponse = await axios.post(`${BASE_URL}/api/v1/exceptions`, exceptionPayload);
      console.log('   ✅ Exception published successfully:', publishResponse.data);
    } catch (error) {
      console.log('   ❌ Exception publishing failed:', error.response?.data || error.message);
    }

    // Test 3: Publish Exception with Minimal Data
    console.log('\n📋 Test 3: Publishing Exception with Minimal Data...');
    const minimalPayload = {
      tenantId: 'minimal-tenant',
      definitionCode: 'SIMPLE_ERROR'
    };

    try {
      const minimalResponse = await axios.post(`${BASE_URL}/api/v1/exceptions`, minimalPayload);
      console.log('   ✅ Minimal exception published successfully:', minimalResponse.data);
    } catch (error) {
      console.log('   ❌ Minimal exception publishing failed:', error.response?.data || error.message);
    }

    // Test 4: Close Business Exception
    console.log('\n📋 Test 4: Closing Business Exception...');
    const closePayload = {
      tenantId: 'test-tenant-123',
      definitionCode: 'PAYMENT_FAILED',
      idempotentKey: 'test-key-' + Date.now(),
      action: ExceptionPublisherAction.RESOLVE,
      closureRemark: 'Issue resolved after customer provided alternative payment method'
    };

    try {
      const closeResponse = await axios.post(`${BASE_URL}/api/v1/exceptions/close`, closePayload);
      console.log('   ✅ Exception closed successfully:', closeResponse.data);
    } catch (error) {
      console.log('   ❌ Exception closing failed:', error.response?.data || error.message);
    }

    // Test 5: Test All Action Types
    console.log('\n📋 Test 5: Testing All Action Types...');
    const actions = [
      { action: ExceptionPublisherAction.RESOLVE, description: 'Resolving exception' },
      { action: ExceptionPublisherAction.ESCALATE, description: 'Escalating to senior team' },
      { action: ExceptionPublisherAction.IGNORE, description: 'Ignoring as known issue' }
    ];

    for (const { action, description } of actions) {
      const actionPayload = {
        tenantId: 'action-test-tenant',
        definitionCode: 'ACTION_TEST',
        idempotentKey: `test-key-${action}-${Date.now()}`,
        action: action,
        closureRemark: description
      };

      try {
        const actionResponse = await axios.post(`${BASE_URL}/api/v1/exceptions/close`, actionPayload);
        console.log(`   ✅ ${action} action successful:`, actionResponse.data.data.action);
      } catch (error) {
        console.log(`   ❌ ${action} action failed:`, error.response?.data || error.message);
      }
    }

    // Test 6: Validation Tests
    console.log('\n📋 Test 6: Validation Tests...');
    
    // Test invalid tenant ID
    try {
      await axios.post(`${BASE_URL}/api/v1/exceptions`, {
        tenantId: 'invalid@tenant!',
        definitionCode: 'VALID_CODE'
      });
      console.log('   ❌ Validation should have failed for invalid tenant ID');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('   ✅ Validation correctly rejected invalid tenant ID');
      } else {
        console.log('   ❌ Unexpected error:', error.message);
      }
    }

    // Test invalid definition code
    try {
      await axios.post(`${BASE_URL}/api/v1/exceptions`, {
        tenantId: 'valid-tenant',
        definitionCode: 'INVALID@CODE!'
      });
      console.log('   ❌ Validation should have failed for invalid definition code');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('   ✅ Validation correctly rejected invalid definition code');
      } else {
        console.log('   ❌ Unexpected error:', error.message);
      }
    }

    // Test 7: Complex Exception Info
    console.log('\n📋 Test 7: Complex Exception Info...');
    const complexPayload = {
      tenantId: 'complex-tenant',
      definitionCode: 'COMPLEX_ERROR',
      exceptionInfo: {
        user: {
          id: 'user-123',
          profile: {
            name: 'John Doe',
            email: '<EMAIL>',
            preferences: {
              notifications: true,
              theme: 'dark'
            }
          }
        },
        transaction: {
          id: 'txn-456',
          items: [
            { id: 'item-1', name: 'Product A', price: 10.99, quantity: 2 },
            { id: 'item-2', name: 'Product B', price: 25.50, quantity: 1 }
          ],
          total: 47.48,
          currency: 'USD',
          paymentMethod: {
            type: 'credit_card',
            last4: '1234',
            brand: 'visa'
          }
        },
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'web-app',
          version: '1.2.3',
          sessionId: 'session-' + Math.random().toString(36).substr(2, 9),
          userAgent: 'Mozilla/5.0 (Test Browser)',
          ipAddress: '*************'
        }
      },
      message: 'Complex transaction failed with multiple nested data points'
    };

    try {
      const complexResponse = await axios.post(`${BASE_URL}/api/v1/exceptions`, complexPayload);
      console.log('   ✅ Complex exception published successfully:', complexResponse.data);
    } catch (error) {
      console.log('   ❌ Complex exception publishing failed:', error.response?.data || error.message);
    }

    console.log('\n🎉 Exception Publishing Tests Completed!');
    console.log('\n📊 Summary:');
    console.log('   ✅ Health check');
    console.log('   ✅ Basic exception publishing');
    console.log('   ✅ Minimal exception publishing');
    console.log('   ✅ Exception closing');
    console.log('   ✅ All action types');
    console.log('   ✅ Validation tests');
    console.log('   ✅ Complex exception info');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Check if service is running
async function checkServiceStatus() {
  try {
    await axios.get(`${BASE_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  console.log('🔍 Checking if Cronus service is running...');
  
  const isRunning = await checkServiceStatus();
  if (!isRunning) {
    console.log('❌ Cronus service is not running on port 9876');
    console.log('💡 Please start the service first:');
    console.log('   cd packages/cronus-service');
    console.log('   npm run start:dev');
    console.log('   or');
    console.log('   npm run build && npm start');
    process.exit(1);
  }

  console.log('✅ Service is running, starting tests...\n');
  await testExceptionPublishing();
}

main().catch(console.error);
