#!/usr/bin/env node

/**
 * <PERSON><PERSON> script to test the Cronus Client library functionality
 */

const { 
  CronusClient, 
  CronusBusinessException, 
  CloseBusinessException,
  DefaultBusinessObservation,
  ExceptionPublisherAction,
  CronusConfigurationLoader,
  WinstonLogger 
} = require('./packages/cronus-client/dist/index.js');

async function runDemo() {
  console.log('🚀 Starting Cronus Client Demo...\n');

  try {
    // Create configuration
    const config = {
      appName: 'cronus-demo',
      clusterName: 'test',
      sso: {
        baseUrl: 'https://sso-test.example.com',
        oauth: {
          appAuthProfileId: 'test-profile',
          appPrivateKey: 'test-key',
          clientId: 'test-client',
          clientSecret: 'test-secret',
          domainId: 'test-domain',
          scope: 'test-scope'
        },
        tokenBufferTime: 3600000
      },
      delta: {
        v2Url: 'https://delta-test.example.com',
        encryptionBase64Secret: 'test-secret'
      },
      schaas: {
        baseUrl: 'https://schaas-test.example.com'
      },
      proteus: {
        endpoint: 'https://proteus-test.example.com',
        certstoreEndpoint: 'https://certstore-test.example.com',
        sessionsEndpoint: 'https://sessions-test.example.com'
      }
    };

    // Create logger
    const logger = new WinstonLogger({ level: 'info' });

    // Create Cronus client
    const cronusClient = CronusClient.create(config, logger);
    console.log('✅ Cronus Client created successfully');

    // Test 1: Create and validate a business exception
    console.log('\n📋 Test 1: Creating Business Exception...');
    const businessException = CronusBusinessException.builder()
      .setTenantId('demo-tenant-123')
      .setBusinessExceptionDefinitionCode('PAYMENT_FAILED')
      .setIdempotentKey('payment-demo-' + Date.now())
      .setExceptionInfo({
        amount: 250.50,
        currency: 'USD',
        orderId: 'order-789',
        paymentMethod: 'credit_card',
        errorCode: 'INSUFFICIENT_FUNDS'
      })
      .setMessage('Payment failed due to insufficient funds')
      .build();

    console.log('   Exception created:', {
      tenantId: businessException.getTenantId(),
      definitionCode: businessException.getBusinessExceptionDefinitionCode(),
      idempotentKey: businessException.getIdempotentKey(),
      message: businessException.message
    });

    // Test 2: Validate the exception
    console.log('\n🔍 Test 2: Validating Business Exception...');
    try {
      cronusClient.getBusinessExceptionValidator().validateBusinessException(businessException);
      console.log('   ✅ Business exception validation passed');
    } catch (error) {
      console.log('   ❌ Business exception validation failed:', error.message);
    }

    // Test 3: Create a close exception
    console.log('\n📋 Test 3: Creating Close Exception...');
    const closeException = CloseBusinessException.builder()
      .setTenantId('demo-tenant-123')
      .setBusinessExceptionDefinitionCode('PAYMENT_FAILED')
      .setIdempotentKey(businessException.getIdempotentKey())
      .setAction(ExceptionPublisherAction.RESOLVE)
      .setClosureRemark('Issue resolved after customer provided alternative payment method')
      .build();

    console.log('   Close exception created:', {
      tenantId: closeException.getTenantId(),
      definitionCode: closeException.getBusinessExceptionDefinitionCode(),
      idempotentKey: closeException.getIdempotentKey(),
      action: closeException.getAction(),
      remark: closeException.getClosureRemark()
    });

    // Test 4: Create a business observation
    console.log('\n📋 Test 4: Creating Business Observation...');
    const businessObservation = DefaultBusinessObservation.builder()
      .setTenantId('demo-tenant-123')
      .setDefinitionCode('USER_LOGIN_SUCCESS')
      .setIdempotentKey('login-demo-' + Date.now())
      .setObservationInfo({
        userId: 'user-456',
        loginTime: new Date().toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Demo Browser)',
        sessionId: 'session-' + Math.random().toString(36).substr(2, 9)
      })
      .setRemarks('Successful login from trusted device')
      .build();

    console.log('   Business observation created:', {
      tenantId: businessObservation.getTenantId(),
      definitionCode: businessObservation.getDefinitionCode(),
      idempotentKey: businessObservation.getIdempotentKey(),
      remarks: businessObservation.getRemarks()
    });

    // Test 5: Test JSON serialization
    console.log('\n📄 Test 5: JSON Serialization...');
    console.log('   Business Exception JSON:', JSON.stringify(businessException.toJSON(), null, 2));
    console.log('   Close Exception JSON:', JSON.stringify(closeException.toJSON(), null, 2));
    console.log('   Business Observation JSON:', JSON.stringify(businessObservation.toJSON(), null, 2));

    // Test 6: Test error handling
    console.log('\n❌ Test 6: Error Handling...');
    try {
      CronusBusinessException.builder()
        .setTenantId('') // Invalid empty tenant ID
        .build();
    } catch (error) {
      console.log('   ✅ Caught expected validation error:', error.message);
    }

    try {
      cronusClient.getBusinessExceptionValidator().validateBusinessException({
        getTenantId: () => 'invalid@tenant',
        getBusinessExceptionDefinitionCode: () => 'VALID_CODE',
        getIdempotentKey: () => 'valid-key',
        getExceptionInfo: () => ({})
      });
    } catch (error) {
      console.log('   ✅ Caught expected validation error:', error.message);
    }

    console.log('\n🎉 Demo completed successfully!');
    console.log('\n📊 Summary:');
    console.log('   ✅ Cronus Client initialization');
    console.log('   ✅ Business Exception creation and validation');
    console.log('   ✅ Close Exception creation');
    console.log('   ✅ Business Observation creation');
    console.log('   ✅ JSON serialization');
    console.log('   ✅ Error handling and validation');
    console.log('\n💡 Note: Actual publishing to Atropos would require proper network connectivity and authentication.');

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the demo
runDemo().catch(console.error);
