import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { 
  PubSubEvent, 
  Tag, 
  NameValuePair, 
  OperationType, 
  TopicScope 
} from '../models/events';
import { CronusConfig } from '../models/interfaces';
import { Logger } from '../utils/Logger';

/**
 * Exception thrown when publishing fails
 */
export class PublishingException extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'PublishingException';
  }
}

/**
 * Client for publishing events to Atropos message queue
 */
export class AtroposPublisherClient {
  private readonly httpClient: AxiosInstance;
  private readonly logger: Logger;
  private readonly jid: string;
  private readonly config: CronusConfig;

  constructor(config: CronusConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.jid = `${Math.floor(Math.random() * 100000)}@${config.appName}.services.olympus`;
    
    this.httpClient = axios.create({
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': `cronus-client-js/${config.appName}`,
        'X-JID': this.jid
      }
    });

    this.setupInterceptors();
  }

  /**
   * Publishes an event to Atropos
   */
  async publish(event: PubSubEvent): Promise<void> {
    try {
      this.logger.info('Publishing event to Atropos', {
        objectType: event.objectType,
        tenant: event.tenant,
        objectId: event.objectId,
        operationType: event.operationType
      });

      const topic = this.constructTopicName(event);
      const publishUrl = await this.getPublishUrl(topic);
      
      const payload = this.constructPayload(event);
      
      await this.httpClient.post(publishUrl, payload);
      
      this.logger.info('Successfully published event to Atropos', {
        topic,
        objectType: event.objectType,
        objectId: event.objectId
      });
    } catch (error) {
      this.logger.error('Failed to publish event to Atropos', {
        error: error instanceof Error ? error.message : String(error),
        objectType: event.objectType,
        tenant: event.tenant,
        objectId: event.objectId
      });
      
      throw new PublishingException(
        `Failed to publish event: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Constructs the topic name for the event
   */
  private constructTopicName(event: PubSubEvent): string {
    return `_${event.topicScope.toLowerCase()}_${event.tenant}_${event.objectType}`;
  }

  /**
   * Gets the publish URL for the topic
   */
  private async getPublishUrl(topic: string): Promise<string> {
    // In a real implementation, this would resolve the actual Atropos endpoint
    // For now, we'll use a placeholder URL structure
    const baseUrl = process.env.ATROPOS_BASE_URL || 'https://atropos.internal.zeta.in';
    return `${baseUrl}/api/v1/publish/${topic}`;
  }

  /**
   * Constructs the payload for the event
   */
  private constructPayload(event: PubSubEvent): any {
    return {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      source: this.jid,
      type: `${event.objectType}.${event.operationType}`,
      data: event.data,
      tenant: event.tenant,
      tags: event.tags,
      objectType: event.objectType,
      operationType: event.operationType,
      objectId: event.objectId,
      stateMachineState: event.stateMachineState,
      topicScope: event.topicScope,
      sourceAttributes: event.sourceAttributes
    };
  }

  /**
   * Sets up HTTP interceptors for authentication and error handling
   */
  private setupInterceptors(): void {
    // Request interceptor for authentication
    this.httpClient.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        // Add authentication token if available
        const token = await this.getAuthToken();
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        this.logger.error('Request interceptor error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.httpClient.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error('HTTP request failed', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          url: error.config?.url,
          method: error.config?.method
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Gets authentication token (placeholder implementation)
   */
  private async getAuthToken(): Promise<string | null> {
    // In a real implementation, this would handle OAuth token generation
    // using the SSO configuration from CronusConfig
    try {
      // Placeholder for token generation logic
      return process.env.ATROPOS_AUTH_TOKEN || null;
    } catch (error) {
      this.logger.warn('Failed to get auth token', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * Creates a tag for event categorization
   */
  static createTag(key: string, value: string): Tag {
    return { key, value };
  }

  /**
   * Creates a name-value pair for source attributes
   */
  static createNameValuePair(name: string, value: string): NameValuePair {
    return { name, value };
  }
}
