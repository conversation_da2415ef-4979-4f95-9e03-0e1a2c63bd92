import { BusinessExceptionValidator, ValidationError } from '../../core/BusinessExceptionValidator';
import { CronusBusinessException } from '../../models/CronusBusinessException';
import { CloseBusinessException } from '../../models/CloseBusinessException';
import { DefaultBusinessObservation } from '../../models/DefaultBusinessObservation';
import { ExceptionPublisherAction } from '../../models/interfaces';

describe('BusinessExceptionValidator', () => {
  let validator: BusinessExceptionValidator;

  beforeEach(() => {
    validator = new BusinessExceptionValidator();
  });

  describe('validateBusinessException', () => {
    it('should validate a valid business exception', () => {
      const exception = new CronusBusinessException(
        'valid-tenant',
        'VALID_CODE',
        'valid-key-123',
        { field: 'value' }
      );

      expect(() => validator.validateBusinessException(exception)).not.toThrow();
    });

    it('should throw ValidationError for missing tenantId', () => {
      const exception = new CronusBusinessException(
        '',
        'VALID_CODE',
        'valid-key-123'
      );

      expect(() => validator.validateBusinessException(exception))
        .toThrow(new ValidationError('tenantId is required', 'tenantId'));
    });

    it('should throw ValidationError for invalid tenantId format', () => {
      const exception = new CronusBusinessException(
        'invalid@tenant',
        'VALID_CODE',
        'valid-key-123'
      );

      expect(() => validator.validateBusinessException(exception))
        .toThrow(new ValidationError('Invalid tenant ID format', 'tenantId'));
    });

    it('should throw ValidationError for missing definition code', () => {
      const exception = new CronusBusinessException(
        'valid-tenant',
        '',
        'valid-key-123'
      );

      expect(() => validator.validateBusinessException(exception))
        .toThrow(new ValidationError('businessExceptionDefinitionCode is required', 'businessExceptionDefinitionCode'));
    });

    it('should throw ValidationError for invalid definition code format', () => {
      const exception = new CronusBusinessException(
        'valid-tenant',
        'INVALID@CODE',
        'valid-key-123'
      );

      expect(() => validator.validateBusinessException(exception))
        .toThrow(new ValidationError('Invalid business exception definition code format', 'businessExceptionDefinitionCode'));
    });

    it('should throw ValidationError for missing idempotent key', () => {
      const exception = new CronusBusinessException(
        'valid-tenant',
        'VALID_CODE',
        ''
      );

      expect(() => validator.validateBusinessException(exception))
        .toThrow(new ValidationError('idempotentKey is required', 'idempotentKey'));
    });

    it('should throw ValidationError for invalid idempotent key format', () => {
      const exception = new CronusBusinessException(
        'valid-tenant',
        'VALID_CODE',
        'invalid@key'
      );

      expect(() => validator.validateBusinessException(exception))
        .toThrow(new ValidationError('Invalid idempotent key format', 'idempotentKey'));
    });

    it('should accept valid UUID as idempotent key', () => {
      const exception = new CronusBusinessException(
        'valid-tenant',
        'VALID_CODE',
        '123e4567-e89b-12d3-a456-************'
      );

      expect(() => validator.validateBusinessException(exception)).not.toThrow();
    });
  });

  describe('validateCloseException', () => {
    it('should validate a valid close exception', () => {
      const closeException = new CloseBusinessException(
        'valid-tenant',
        'VALID_CODE',
        'valid-key-123',
        ExceptionPublisherAction.RESOLVE,
        'Test closure remark'
      );

      expect(() => validator.validateCloseException(closeException)).not.toThrow();
    });

    it('should throw ValidationError for missing action', () => {
      // Create a mock close exception with undefined action
      const mockCloseException = {
        getTenantId: () => 'valid-tenant',
        getBusinessExceptionDefinitionCode: () => 'VALID_CODE',
        getIdempotentKey: () => 'valid-key-123',
        getAction: () => undefined,
        getClosureRemark: () => 'Test remark'
      } as any;

      expect(() => validator.validateCloseException(mockCloseException))
        .toThrow(new ValidationError('action is required', 'action'));
    });

    it('should throw ValidationError for missing closure remark', () => {
      // Create a mock close exception with empty closure remark
      const mockCloseException = {
        getTenantId: () => 'valid-tenant',
        getBusinessExceptionDefinitionCode: () => 'VALID_CODE',
        getIdempotentKey: () => 'valid-key-123',
        getAction: () => ExceptionPublisherAction.RESOLVE,
        getClosureRemark: () => ''
      } as any;

      expect(() => validator.validateCloseException(mockCloseException))
        .toThrow(new ValidationError('closureRemark is required', 'closureRemark'));
    });
  });

  describe('validateBusinessObservation', () => {
    it('should validate a valid business observation', () => {
      const observation = new DefaultBusinessObservation(
        'valid-tenant',
        'VALID_CODE',
        'valid-key-123',
        { field: 'value' },
        'Test remarks'
      );

      expect(() => validator.validateBusinessObservation(observation)).not.toThrow();
    });

    it('should throw ValidationError for missing tenant ID', () => {
      const observation = new DefaultBusinessObservation(
        '',
        'VALID_CODE',
        'valid-key-123'
      );

      expect(() => validator.validateBusinessObservation(observation))
        .toThrow(new ValidationError('tenantId is required', 'tenantId'));
    });

    it('should throw ValidationError for missing definition code', () => {
      const observation = new DefaultBusinessObservation(
        'valid-tenant',
        '',
        'valid-key-123'
      );

      expect(() => validator.validateBusinessObservation(observation))
        .toThrow(new ValidationError('definitionCode is required', 'definitionCode'));
    });

    it('should throw ValidationError for missing idempotent key', () => {
      const observation = new DefaultBusinessObservation(
        'valid-tenant',
        'VALID_CODE',
        ''
      );

      expect(() => validator.validateBusinessObservation(observation))
        .toThrow(new ValidationError('idempotentKey is required', 'idempotentKey'));
    });
  });

  describe('ValidationError', () => {
    it('should create ValidationError with message and field', () => {
      const error = new ValidationError('Test message', 'testField');

      expect(error.message).toBe('Test message');
      expect(error.field).toBe('testField');
      expect(error.name).toBe('ValidationError');
    });
  });
});
