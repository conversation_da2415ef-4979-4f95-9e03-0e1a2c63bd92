import { 
  CronusClient, 
  CronusBusinessException, 
  CloseBusinessException,
  DefaultBusinessObservation,
  ExceptionPublisherAction,
  CronusConfigurationLoader,
  WinstonLogger
} from '@zeta/cronus-client';
import { v4 as uuidv4 } from 'uuid';
import { EnvironmentConfig } from '../config/environment';

/**
 * Service class that demonstrates usage of Cronus Client
 */
export class CronusService {
  private readonly cronusClient: CronusClient;
  private readonly logger: WinstonLogger;

  constructor(envConfig: EnvironmentConfig) {
    this.logger = new WinstonLogger({
      level: envConfig.logLevel
    });

    // Create Cronus configuration from environment
    const cronusConfig = CronusConfigurationLoader.fromObject({
      appName: envConfig.appName,
      clusterName: envConfig.clusterName,
      sso: {
        baseUrl: envConfig.ssoBaseUrl,
        oauth: {
          appAuthProfileId: envConfig.cruxLltGodOauthAppAuthProfileId,
          appPrivateKey: envConfig.cruxLltGodOauthAppPrivateKey,
          clientId: envConfig.cruxLltGodOauthClientId,
          clientSecret: envConfig.cruxLltGodOauthClientSecret,
          domainId: envConfig.cruxLltGodOauthDomainId,
          scope: envConfig.cruxLltGodOauthScope
        },
        tokenBufferTime: envConfig.cruxLltTokenBufferTime
      },
      delta: {
        v2Url: envConfig.deltaV2Url,
        encryptionBase64Secret: envConfig.deltaV2EncryptionBase64Secret
      },
      schaas: {
        baseUrl: envConfig.schaasBaseUrl
      },
      proteus: {
        endpoint: envConfig.proteusEndpoint,
        certstoreEndpoint: envConfig.certstoreProteusEndpoint,
        sessionsEndpoint: envConfig.sessionsProteusEndpoint
      }
    });

    this.cronusClient = CronusClient.create(cronusConfig, this.logger);
  }

  /**
   * Publishes a business exception
   */
  async publishBusinessException(
    tenantId: string,
    definitionCode: string,
    exceptionInfo: Record<string, any> = {},
    message?: string
  ): Promise<void> {
    try {
      const idempotentKey = uuidv4();
      
      const builder = CronusBusinessException.builder()
        .setTenantId(tenantId)
        .setBusinessExceptionDefinitionCode(definitionCode)
        .setIdempotentKey(idempotentKey)
        .setExceptionInfo(exceptionInfo);

      if (message) {
        builder.setMessage(message);
      }

      const businessException = builder.build();

      await this.cronusClient.getBusinessExceptionHandler().publish(businessException);
      
      this.logger.info('Business exception published successfully', {
        tenantId,
        definitionCode,
        idempotentKey
      });
    } catch (error) {
      this.logger.error('Failed to publish business exception', {
        tenantId,
        definitionCode,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Closes a business exception
   */
  async closeBusinessException(
    tenantId: string,
    definitionCode: string,
    idempotentKey: string,
    action: ExceptionPublisherAction,
    closureRemark: string
  ): Promise<void> {
    try {
      const closeException = CloseBusinessException.builder()
        .setTenantId(tenantId)
        .setBusinessExceptionDefinitionCode(definitionCode)
        .setIdempotentKey(idempotentKey)
        .setAction(action)
        .setClosureRemark(closureRemark)
        .build();

      await this.cronusClient.getBusinessExceptionHandler().close(closeException);
      
      this.logger.info('Business exception closed successfully', {
        tenantId,
        definitionCode,
        idempotentKey,
        action
      });
    } catch (error) {
      this.logger.error('Failed to close business exception', {
        tenantId,
        definitionCode,
        idempotentKey,
        action,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Publishes a business observation
   */
  async publishBusinessObservation(
    tenantId: string,
    definitionCode: string,
    observationInfo: Record<string, any> = {},
    remarks: string = ''
  ): Promise<void> {
    try {
      const idempotentKey = uuidv4();
      
      const businessObservation = DefaultBusinessObservation.builder()
        .setTenantId(tenantId)
        .setDefinitionCode(definitionCode)
        .setIdempotentKey(idempotentKey)
        .setObservationInfo(observationInfo)
        .setRemarks(remarks)
        .build();

      await this.cronusClient.getBusinessObservationHandler().publish(businessObservation);
      
      this.logger.info('Business observation published successfully', {
        tenantId,
        definitionCode,
        idempotentKey
      });
    } catch (error) {
      this.logger.error('Failed to publish business observation', {
        tenantId,
        definitionCode,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Resolves a business observation
   */
  async resolveBusinessObservation(
    tenantId: string,
    definitionCode: string,
    idempotentKey: string,
    observationInfo: Record<string, any> = {},
    remarks: string = ''
  ): Promise<void> {
    try {
      const businessObservation = DefaultBusinessObservation.builder()
        .setTenantId(tenantId)
        .setDefinitionCode(definitionCode)
        .setIdempotentKey(idempotentKey)
        .setObservationInfo(observationInfo)
        .setRemarks(remarks)
        .build();

      await this.cronusClient.getBusinessObservationHandler().resolve(businessObservation);
      
      this.logger.info('Business observation resolved successfully', {
        tenantId,
        definitionCode,
        idempotentKey
      });
    } catch (error) {
      this.logger.error('Failed to resolve business observation', {
        tenantId,
        definitionCode,
        idempotentKey,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Gets the Cronus client instance
   */
  getCronusClient(): CronusClient {
    return this.cronusClient;
  }

  /**
   * Health check method
   */
  async healthCheck(): Promise<{ status: string; timestamp: string; config: any }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      config: {
        appName: this.cronusClient.getConfig().appName,
        clusterName: this.cronusClient.getConfig().clusterName
      }
    };
  }
}
