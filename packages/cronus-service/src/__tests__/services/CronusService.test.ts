import { CronusService } from '../../services/CronusService';
import { EnvironmentConfig } from '../../config/environment';
import { ExceptionPublisherAction, CronusClient } from '@zeta/cronus-client';
import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.join(__dirname, '../../../.env.test') });

// Mock the CronusClient to avoid actual network calls
jest.mock('@zeta/cronus-client', () => {
  const originalModule = jest.requireActual('@zeta/cronus-client');

  return {
    ...originalModule,
    CronusClient: {
      create: jest.fn(() => ({
        getConfig: jest.fn(() => ({
          appName: 'test-app',
          clusterName: 'test-cluster'
        })),
        getBusinessExceptionHandler: jest.fn(() => ({
          publish: jest.fn().mockResolvedValue(undefined),
          close: jest.fn().mockResolvedValue(undefined)
        })),
        getBusinessObservationHandler: jest.fn(() => ({
          publish: jest.fn().mockResolvedValue(undefined),
          resolve: jest.fn().mockResolvedValue(undefined)
        })),
        getBusinessExceptionValidator: jest.fn(() => ({
          validateBusinessException: jest.fn()
        }))
      }))
    }
  };
});

describe('CronusService', () => {
  let cronusService: CronusService;
  let testConfig: EnvironmentConfig;

  beforeAll(() => {
    testConfig = {
      port: 3001,
      nodeEnv: 'test',
      appName: 'cronus-service-test',
      clusterName: 'test-cluster',
      ssoBaseUrl: 'https://sso-test.example.com',
      cruxLltGodOauthAppAuthProfileId: 'test-profile-id',
      cruxLltGodOauthAppPrivateKey: 'test-private-key',
      cruxLltGodOauthClientId: 'test-client-id',
      cruxLltGodOauthClientSecret: 'test-client-secret',
      cruxLltGodOauthDomainId: 'test-domain',
      cruxLltGodOauthScope: 'test-scope',
      cruxLltTokenBufferTime: 3600000,
      deltaV2Url: 'https://delta-test.example.com',
      deltaV2EncryptionBase64Secret: 'dGVzdC1lbmNyeXB0aW9uLXNlY3JldA==',
      schaasBaseUrl: 'https://schaas-test.example.com',
      proteusEndpoint: 'https://proteus-test.example.com',
      certstoreProteusEndpoint: 'https://certstore-test.example.com',
      sessionsProteusEndpoint: 'https://sessions-test.example.com',
      logLevel: 'debug',
      rateLimitWindowMs: 900000,
      rateLimitMaxRequests: 1000
    };

    cronusService = new CronusService(testConfig);
  });

  describe('publishBusinessException', () => {
    it('should publish a business exception successfully', async () => {
      const tenantId = 'test-tenant-123';
      const definitionCode = 'PAYMENT_FAILED';
      const exceptionInfo = {
        amount: 250.50,
        currency: 'USD',
        orderId: 'order-789',
        paymentMethod: 'credit_card',
        errorCode: 'INSUFFICIENT_FUNDS'
      };
      const message = 'Payment failed due to insufficient funds';

      // This should not throw an error
      await expect(
        cronusService.publishBusinessException(tenantId, definitionCode, exceptionInfo, message)
      ).resolves.not.toThrow();
    });

    it('should publish exception with minimal parameters', async () => {
      const tenantId = 'minimal-tenant';
      const definitionCode = 'SIMPLE_ERROR';

      await expect(
        cronusService.publishBusinessException(tenantId, definitionCode)
      ).resolves.not.toThrow();
    });

    it('should handle empty exceptionInfo object', async () => {
      const tenantId = 'empty-info-tenant';
      const definitionCode = 'EMPTY_INFO_ERROR';
      const exceptionInfo = {};

      await expect(
        cronusService.publishBusinessException(tenantId, definitionCode, exceptionInfo)
      ).resolves.not.toThrow();
    });

    it('should handle complex nested exceptionInfo', async () => {
      const tenantId = 'complex-tenant';
      const definitionCode = 'COMPLEX_ERROR';
      const exceptionInfo = {
        user: {
          id: 'user-123',
          profile: {
            name: 'John Doe',
            email: '<EMAIL>'
          }
        },
        transaction: {
          id: 'txn-456',
          items: [
            { id: 'item-1', price: 10.99 },
            { id: 'item-2', price: 25.50 }
          ],
          total: 36.49
        },
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'web-app',
          version: '1.2.3'
        }
      };

      await expect(
        cronusService.publishBusinessException(tenantId, definitionCode, exceptionInfo)
      ).resolves.not.toThrow();
    });
  });

  describe('closeBusinessException', () => {
    it('should close a business exception with RESOLVE action', async () => {
      const tenantId = 'test-tenant-123';
      const definitionCode = 'PAYMENT_FAILED';
      const idempotentKey = 'test-key-resolve-' + Date.now();
      const action = ExceptionPublisherAction.RESOLVE;
      const closureRemark = 'Issue resolved after customer provided alternative payment method';

      await expect(
        cronusService.closeBusinessException(tenantId, definitionCode, idempotentKey, action, closureRemark)
      ).resolves.not.toThrow();
    });

    it('should close a business exception with ESCALATE action', async () => {
      const tenantId = 'test-tenant-123';
      const definitionCode = 'PAYMENT_FAILED';
      const idempotentKey = 'test-key-escalate-' + Date.now();
      const action = ExceptionPublisherAction.ESCALATE;
      const closureRemark = 'Escalating to senior support team for further investigation';

      await expect(
        cronusService.closeBusinessException(tenantId, definitionCode, idempotentKey, action, closureRemark)
      ).resolves.not.toThrow();
    });

    it('should close a business exception with IGNORE action', async () => {
      const tenantId = 'test-tenant-123';
      const definitionCode = 'PAYMENT_FAILED';
      const idempotentKey = 'test-key-ignore-' + Date.now();
      const action = ExceptionPublisherAction.IGNORE;
      const closureRemark = 'Ignoring as this is a known issue with temporary impact';

      await expect(
        cronusService.closeBusinessException(tenantId, definitionCode, idempotentKey, action, closureRemark)
      ).resolves.not.toThrow();
    });
  });

  describe('publishBusinessObservation', () => {
    it('should publish a business observation successfully', async () => {
      const tenantId = 'test-tenant-123';
      const definitionCode = 'USER_LOGIN_SUCCESS';
      const observationInfo = {
        userId: 'user-456',
        loginTime: new Date().toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Test Browser)',
        sessionId: 'session-' + Math.random().toString(36).substr(2, 9)
      };
      const remarks = 'Successful login from trusted device';

      await expect(
        cronusService.publishBusinessObservation(tenantId, definitionCode, observationInfo, remarks)
      ).resolves.not.toThrow();
    });

    it('should publish observation with minimal parameters', async () => {
      const tenantId = 'minimal-tenant';
      const definitionCode = 'SIMPLE_OBSERVATION';

      await expect(
        cronusService.publishBusinessObservation(tenantId, definitionCode)
      ).resolves.not.toThrow();
    });
  });

  describe('resolveBusinessObservation', () => {
    it('should resolve a business observation successfully', async () => {
      const tenantId = 'test-tenant-123';
      const definitionCode = 'USER_LOGIN_SUCCESS';
      const idempotentKey = 'observation-key-' + Date.now();
      const observationInfo = {
        resolution: 'completed',
        resolvedBy: 'system'
      };
      const remarks = 'Observation resolved automatically';

      await expect(
        cronusService.resolveBusinessObservation(tenantId, definitionCode, idempotentKey, observationInfo, remarks)
      ).resolves.not.toThrow();
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const health = await cronusService.healthCheck();

      expect(health).toEqual({
        status: 'healthy',
        timestamp: expect.any(String),
        config: {
          appName: 'test-app', // This comes from the mock
          clusterName: 'test-cluster'
        }
      });

      // Verify timestamp is valid ISO string
      expect(new Date(health.timestamp).toISOString()).toBe(health.timestamp);
    });
  });

  describe('getCronusClient', () => {
    it('should return the Cronus client instance', () => {
      const client = cronusService.getCronusClient();
      expect(client).toBeDefined();
      expect(client.getConfig).toBeDefined();
      expect(client.getBusinessExceptionHandler).toBeDefined();
      expect(client.getBusinessObservationHandler).toBeDefined();
    });

    it('should return client with correct configuration', () => {
      const client = cronusService.getCronusClient();
      const config = client.getConfig();

      expect(config.appName).toBe('test-app'); // This comes from the mock
      expect(config.clusterName).toBe('test-cluster');
    });
  });
});
