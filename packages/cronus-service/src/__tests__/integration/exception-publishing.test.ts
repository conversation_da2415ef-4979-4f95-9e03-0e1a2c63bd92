import request from 'supertest';
import { Application } from 'express';
import { createApp } from '../../app';
import { loadEnvironmentConfig } from '../../config/environment';
import { ExceptionPublisherAction } from '@zeta/cronus-client';
import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.join(__dirname, '../../../.env.test') });

// Mock the CronusClient to avoid actual network calls
jest.mock('@zeta/cronus-client', () => {
  const originalModule = jest.requireActual('@zeta/cronus-client');

  return {
    ...originalModule,
    CronusClient: {
      create: jest.fn(() => ({
        getConfig: jest.fn(() => ({
          appName: 'test-app',
          clusterName: 'test-cluster'
        })),
        getBusinessExceptionHandler: jest.fn(() => ({
          publish: jest.fn().mockResolvedValue(undefined),
          close: jest.fn().mockResolvedValue(undefined)
        })),
        getBusinessObservationHandler: jest.fn(() => ({
          publish: jest.fn().mockResolvedValue(undefined),
          resolve: jest.fn().mockResolvedValue(undefined)
        })),
        getBusinessExceptionValidator: jest.fn(() => ({
          validateBusinessException: jest.fn()
        }))
      }))
    }
  };
});

describe('Exception Publishing Integration Tests', () => {
  let app: Application;
  let server: any;

  beforeAll(async () => {
    // Load test configuration
    const envConfig = loadEnvironmentConfig();
    
    // Create Express app with test configuration
    app = createApp(envConfig);
    
    // Start server on test port
    server = app.listen(envConfig.port);
  });

  afterAll(async () => {
    if (server) {
      await new Promise<void>((resolve) => {
        server.close(() => resolve());
      });
    }
  });

  describe('POST /api/v1/exceptions', () => {
    it('should publish a business exception successfully', async () => {
      const payload = {
        tenantId: 'test-tenant-123',
        definitionCode: 'PAYMENT_FAILED',
        exceptionInfo: {
          amount: 250.50,
          currency: 'USD',
          orderId: 'order-789',
          paymentMethod: 'credit_card',
          errorCode: 'INSUFFICIENT_FUNDS'
        },
        message: 'Payment failed due to insufficient funds'
      };

      const response = await request(app)
        .post('/api/v1/exceptions')
        .send(payload)
        .expect(201);

      expect(response.body).toEqual({
        success: true,
        message: 'Business exception published successfully',
        data: {
          tenantId: payload.tenantId,
          definitionCode: payload.definitionCode,
          timestamp: expect.any(String)
        }
      });

      // Verify timestamp is valid ISO string
      expect(new Date(response.body.data.timestamp).toISOString()).toBe(response.body.data.timestamp);
    });

    it('should publish exception with minimal required fields', async () => {
      const payload = {
        tenantId: 'minimal-tenant',
        definitionCode: 'SIMPLE_ERROR'
      };

      const response = await request(app)
        .post('/api/v1/exceptions')
        .send(payload)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.tenantId).toBe(payload.tenantId);
      expect(response.body.data.definitionCode).toBe(payload.definitionCode);
    });

    it('should validate tenantId format', async () => {
      const payload = {
        tenantId: 'invalid@tenant!',
        definitionCode: 'VALID_CODE'
      };

      const response = await request(app)
        .post('/api/v1/exceptions')
        .send(payload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation failed');
      expect(response.body.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            path: 'tenantId'
          })
        ])
      );
    });

    it('should validate definitionCode format', async () => {
      const payload = {
        tenantId: 'valid-tenant',
        definitionCode: 'INVALID@CODE!'
      };

      const response = await request(app)
        .post('/api/v1/exceptions')
        .send(payload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            path: 'definitionCode'
          })
        ])
      );
    });

    it('should validate message length', async () => {
      const payload = {
        tenantId: 'test-tenant',
        definitionCode: 'TEST_CODE',
        message: 'x'.repeat(501) // Exceeds 500 character limit
      };

      const response = await request(app)
        .post('/api/v1/exceptions')
        .send(payload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            path: 'message'
          })
        ])
      );
    });

    it('should handle complex exceptionInfo object', async () => {
      const payload = {
        tenantId: 'complex-tenant',
        definitionCode: 'COMPLEX_ERROR',
        exceptionInfo: {
          nested: {
            object: {
              with: 'multiple levels'
            }
          },
          array: [1, 2, 3],
          boolean: true,
          number: 42,
          nullValue: null
        },
        message: 'Complex exception with nested data'
      };

      const response = await request(app)
        .post('/api/v1/exceptions')
        .send(payload)
        .expect(201);

      expect(response.body.success).toBe(true);
    });
  });

  describe('POST /api/v1/exceptions/close', () => {
    it('should close a business exception successfully', async () => {
      const payload = {
        tenantId: 'test-tenant-123',
        definitionCode: 'PAYMENT_FAILED',
        idempotentKey: 'test-key-' + Date.now(),
        action: ExceptionPublisherAction.RESOLVE,
        closureRemark: 'Issue resolved after customer provided alternative payment method'
      };

      const response = await request(app)
        .post('/api/v1/exceptions/close')
        .send(payload)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Business exception closed successfully',
        data: {
          tenantId: payload.tenantId,
          definitionCode: payload.definitionCode,
          idempotentKey: payload.idempotentKey,
          action: payload.action,
          timestamp: expect.any(String)
        }
      });
    });

    it('should validate required fields for closing exception', async () => {
      const payload = {
        tenantId: 'test-tenant',
        definitionCode: 'TEST_CODE'
        // Missing idempotentKey, action, and closureRemark
      };

      const response = await request(app)
        .post('/api/v1/exceptions/close')
        .send(payload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors.length).toBeGreaterThan(0);
    });

    it('should validate action enum values', async () => {
      const payload = {
        tenantId: 'test-tenant',
        definitionCode: 'TEST_CODE',
        idempotentKey: 'test-key',
        action: 'INVALID_ACTION',
        closureRemark: 'Test remark'
      };

      const response = await request(app)
        .post('/api/v1/exceptions/close')
        .send(payload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            path: 'action'
          })
        ])
      );
    });

    it('should test all valid action types', async () => {
      const actions = [
        ExceptionPublisherAction.RESOLVE,
        ExceptionPublisherAction.ESCALATE,
        ExceptionPublisherAction.IGNORE
      ];

      for (const action of actions) {
        const payload = {
          tenantId: 'test-tenant',
          definitionCode: 'TEST_CODE',
          idempotentKey: `test-key-${action}-${Date.now()}`,
          action: action,
          closureRemark: `Testing ${action} action`
        };

        const response = await request(app)
          .post('/api/v1/exceptions/close')
          .send(payload)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.action).toBe(action);
      }
    });
  });

  describe('Health Check Endpoints', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        status: 'healthy',
        timestamp: expect.any(String),
        config: expect.objectContaining({
          appName: expect.any(String),
          clusterName: expect.any(String)
        })
      });
    });

    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/readiness')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.ready).toBe(true);
      expect(response.body.status).toBe('healthy');
    });

    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/liveness')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.alive).toBe(true);
      expect(response.body.timestamp).toBeDefined();
    });
  });
});
