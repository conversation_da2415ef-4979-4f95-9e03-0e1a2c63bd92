import { Router } from 'express';
import { ExceptionController } from '../controllers/ExceptionController';
import { ObservationController } from '../controllers/ObservationController';
import { HealthController } from '../controllers/HealthController';
import { CronusService } from '../services/CronusService';

/**
 * Creates and configures all application routes
 */
export function createRoutes(cronusService: CronusService): Router {
  const router = Router();
  
  // Initialize controllers
  const exceptionController = new ExceptionController(cronusService);
  const observationController = new ObservationController(cronusService);
  const healthController = new HealthController(cronusService);

  // Health check routes
  router.get('/health', healthController.healthCheck.bind(healthController));
  router.get('/health/readiness', healthController.readiness.bind(healthController));
  router.get('/health/liveness', healthController.liveness.bind(healthController));
  router.get('/status', healthController.status.bind(healthController));

  // Business exception routes
  router.post('/api/v1/exceptions', 
    // ExceptionController.publishValidationRules(),
    exceptionController.publishException.bind(exceptionController)
  );
  
  router.post('/api/v1/exceptions/close',
    ExceptionController.closeValidationRules(),
    exceptionController.closeException.bind(exceptionController)
  );

  // Business observation routes
  router.post('/api/v1/observations',
    ObservationController.publishValidationRules(),
    observationController.publishObservation.bind(observationController)
  );
  
  router.post('/api/v1/observations/resolve',
    ObservationController.resolveValidationRules(),
    observationController.resolveObservation.bind(observationController)
  );

  return router;
}
