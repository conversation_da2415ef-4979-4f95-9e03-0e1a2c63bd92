# Test Environment Configuration for Cronus Service
NODE_ENV=test
PORT=3001

# Application Configuration
APP_NAME=cronus-service-test
CLUSTER_NAME=test-cluster

# SSO Configuration (Test values)
SSO_BASE_URL=https://sso-test.example.com
CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID=test-profile-id
CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY=test-private-key
CRUX_LLT_GOD_OAUTH_CLIENT_ID=test-client-id
CRUX_LLT_GOD_OAUTH_CLIENT_SECRET=test-client-secret
CRUX_LLT_GOD_OAUTH_DOMAIN_ID=test-domain
CRUX_LLT_GOD_OAUTH_SCOPE=test-scope
CRUX_LLT_TOKEN_BUFFER_TIME=3600000

# Delta Configuration (Test values)
DELTA_V2_URL=https://delta-test.example.com
DELTA_V2_ENCRYPTION_BASE64_SECRET=dGVzdC1lbmNyeXB0aW9uLXNlY3JldA==

# Schaas Configuration (Test values)
SCHAAS_BASE_URL=https://schaas-test.example.com

# Proteus Configuration (Test values)
PROTEUS_ENDPOINT=https://proteus-test.example.com
CERTSTORE_PROTEUS_ENDPOINT=https://certstore-test.example.com
SESSIONS_PROTEUS_ENDPOINT=https://sessions-test.example.com

# Logging
LOG_LEVEL=debug

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
