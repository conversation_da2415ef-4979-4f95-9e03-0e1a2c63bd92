{"name": "cronus-client-workspace", "version": "1.0.0", "description": "Cronus Client JavaScript Library and Node.js Service Workspace", "private": true, "workspaces": ["packages/cronus-client", "packages/cronus-service"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "eslint packages/*/src/**/*.ts", "clean": "npm run clean --workspaces"}, "devDependencies": {"@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}